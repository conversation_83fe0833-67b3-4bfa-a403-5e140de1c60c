schema {
  query: Query
  mutation: Mutation
}

type AtmosphericReading {
  oxygen: String
  explosive: String
  toxic: String
  co2: String
}

type AuthorisedPerson {
  workerId: Int!
  name: String!
  signatureFileId: String!
  signedAt: DateTime!
}

type Category {
  id: Int!
  description: String!
  jobs: [Job]
  createdAt: DateTime!
  createdBy: String!
  updatedAt: DateTime
  updatedBy: String
  isDeleted: Boolean!
  deletedAt: DateTime
  deletedBy: String
}

type CompetentPerson {
  workerId: Int!
  name: String!
  signatureFileId: String!
  signedAt: DateTime!
}

type ConfinedSpacePermit {
  workersHaveBeenTrained: Boolean!
  nameOfTrainingOrganization: String
  topReading: AtmosphericReading
  midReading: AtmosphericReading
  bottomReading: AtmosphericReading
  emergencyGuidelines: String
  taskObserver: TaskObserver
  id: Int!
  jobId: Int!
  job: Job!
  ptwRefNumber: String!
  projectName: String!
  startingDateTime: DateTime!
  endingDateTime: DateTime!
  description: String!
  location: String!
  hazards: String!
  precautionsRequired: String!
  ppe: String!
  status: PermitStatus!
  daysValid: Int!
  permitIssuer: PermitIssuer!
  permitReturn: PermitReturn
  signOff: SignOff!
  otherPermitsInUse: [Permit!]!
  referencedByPermits: [Permit!]!
  documents: [DocumentFile!]!
  permitType: PermitType!
  createdAt: DateTime!
  createdBy: String!
  updatedAt: DateTime
  updatedBy: String!
  isDeleted: Boolean!
  deletedAt: DateTime
  deletedBy: String
}

type ControlMeasure {
  id: Int!
  description: String!
  closed: Boolean!
  hazardId: Int!
  hazard: Hazard
  createdAt: DateTime!
  createdBy: String!
  updatedAt: DateTime
  updatedBy: String
  isDeleted: Boolean!
  deletedAt: DateTime
  deletedBy: String
}

"Represents a document file with a custom name"
type DocumentFile {
  "Unique identifier for the document file"
  id: Int!
  "Custom name/title for the document"
  name: String!
  "Foreign key to the file metadata"
  fileMetadataId: Int!
  "The underlying file metadata"
  fileMetadata: FileMetadata!
  "URL to access the file"
  url: String!
  "When the document file was created"
  createdAt: DateTime!
  "Who created the document file"
  createdBy: String!
  "When the document file was last updated"
  updatedAt: DateTime
  "Who last updated the document file"
  updatedBy: String!
}

type Equipment {
  id: Int!
  name: String!
  description: String
  serialNumber: String
  model: String
  manufacturer: String
  purchaseDate: DateTime
  lastMaintenanceDate: DateTime
  nextMaintenanceDate: DateTime
  location: String
  status: String
  purchasePrice: Decimal
  category: String
  createdAt: DateTime!
  createdBy: String!
  updatedAt: DateTime
  updatedBy: String
  isDeleted: Boolean!
  deletedAt: DateTime
  deletedBy: String
}

type ExcavationWorkPermit {
  depthOfExcavation: String
  protectionSystems: String
  listOfEquipmentToBeUsed: String
  inspections: String
  inspectionAuthorization: InspectionAuthorization
  id: Int!
  jobId: Int!
  job: Job!
  ptwRefNumber: String!
  projectName: String!
  startingDateTime: DateTime!
  endingDateTime: DateTime!
  description: String!
  location: String!
  hazards: String!
  precautionsRequired: String!
  ppe: String!
  status: PermitStatus!
  daysValid: Int!
  permitIssuer: PermitIssuer!
  permitReturn: PermitReturn
  signOff: SignOff!
  otherPermitsInUse: [Permit!]!
  referencedByPermits: [Permit!]!
  documents: [DocumentFile!]!
  permitType: PermitType!
  createdAt: DateTime!
  createdBy: String!
  updatedAt: DateTime
  updatedBy: String!
  isDeleted: Boolean!
  deletedAt: DateTime
  deletedBy: String
}

"Represents file metadata stored in the system"
type FileMetadata {
  "Unique identifier for the file metadata"
  id: Int!
  "Original name of the uploaded file"
  fileName: String!
  "MIME type of the file"
  contentType: String!
  "Size of the file in bytes"
  size: Long!
  "Optional description of the file"
  description: String
  "MinIO bucket where the file is stored"
  bucketName: String!
  "Unique object key in the MinIO bucket"
  objectKey: String!
  "Entity tag for the file"
  eTag: String
  "Version identifier for the file"
  version: String
  "Type of the file (JPEG, PNG, PDF, etc.)"
  fileType: AllowedFileType!
  "Optional folder path within the bucket"
  folderPath: String
  "Whether the file is publicly accessible"
  isPublic: Boolean!
  "Optional expiration date for the file"
  expiresAt: DateTime
  "Additional metadata stored as JSON"
  additionalMetadata: String
  "URL to access the file"
  url: String!
  "When the file metadata was created"
  createdAt: DateTime!
  "Who created the file metadata"
  createdBy: String!
  "When the file metadata was last updated"
  updatedAt: DateTime
  "Who last updated the file metadata"
  updatedBy: String!
  "Gets the full object path including folder"
  fullObjectPath: String!
  "Checks if the file has expired"
  isExpired: Boolean!
  workersWithProfilePicture: [Worker!]
  toolboxSessions: [ToolboxSession!]
}

type FileUploadResponse {
  success: Boolean!
  errorMessage: String
  fileMetadata: FileMetadata
  presignedUrl: String
}

type FireSafetySupervisor {
  workerId: Int!
  name: String!
  signatureFileId: String!
  signedAt: DateTime!
}

type GeneralWorkPermit {
  isolation: String
  workAreaInspectionAndPermitRenewal: [WorkAreaInspection]
  id: Int!
  jobId: Int!
  job: Job!
  ptwRefNumber: String!
  projectName: String!
  startingDateTime: DateTime!
  endingDateTime: DateTime!
  description: String!
  location: String!
  hazards: String!
  precautionsRequired: String!
  ppe: String!
  status: PermitStatus!
  daysValid: Int!
  permitIssuer: PermitIssuer!
  permitReturn: PermitReturn
  signOff: SignOff!
  otherPermitsInUse: [Permit!]!
  referencedByPermits: [Permit!]!
  documents: [DocumentFile!]!
  permitType: PermitType!
  createdAt: DateTime!
  createdBy: String!
  updatedAt: DateTime
  updatedBy: String!
  isDeleted: Boolean!
  deletedAt: DateTime
  deletedBy: String
}

type Hazard {
  id: Int!
  description: String!
  jobId: Int!
  job: Job
  controlMeasures: [ControlMeasure]
  createdAt: DateTime!
  createdBy: String!
  updatedAt: DateTime
  updatedBy: String
  isDeleted: Boolean!
  deletedAt: DateTime
  deletedBy: String
}

type HotWorkPermit {
  natureOfWork: String
  fireExtinguishers: String
  fireSafetySupervisor: FireSafetySupervisor
  id: Int!
  jobId: Int!
  job: Job!
  ptwRefNumber: String!
  projectName: String!
  startingDateTime: DateTime!
  endingDateTime: DateTime!
  description: String!
  location: String!
  hazards: String!
  precautionsRequired: String!
  ppe: String!
  status: PermitStatus!
  daysValid: Int!
  permitIssuer: PermitIssuer!
  permitReturn: PermitReturn
  signOff: SignOff!
  otherPermitsInUse: [Permit!]!
  referencedByPermits: [Permit!]!
  documents: [DocumentFile!]!
  permitType: PermitType!
  createdAt: DateTime!
  createdBy: String!
  updatedAt: DateTime
  updatedBy: String!
  isDeleted: Boolean!
  deletedAt: DateTime
  deletedBy: String
}

type Incident {
  id: Int!
  title: String!
  description: String!
  incidentDate: DateTime!
  location: String!
  status: IncidentStatus!
  reportedBy: String
  investigatedBy: String
  resolution: String
  resolvedDate: DateTime
  workers: [Worker]
  createdAt: DateTime!
  createdBy: String!
  updatedAt: DateTime
  updatedBy: String!
  isDeleted: Boolean!
  deletedAt: DateTime
  deletedBy: String
}

type InspectionAuthorization {
  nameOfInspector: String!
  designation: String!
  dateOfInspection: DateTime!
  comments: String
}

type Job {
  id: Int!
  title: String!
  description: String
  status: JobStatus!
  requiredPermits: [PermitType]
  timeForCompletion: String
  startDate: DateTime!
  dueDate: DateTime
  calculatedDueDate: DateTime!
  categoryId: Int
  category: Category
  requestedById: Int
  requestedBy: Worker
  requestedDate: DateTime
  blockedById: Int
  blockedBy: Worker
  blockedDate: DateTime
  reviewedById: Int
  reviewedBy: Worker
  reviewedDate: DateTime
  approvedById: Int
  approvedBy: Worker
  approvedDate: DateTime
  finishedById: Int
  finishedBy: Worker
  finishedDate: DateTime
  chiefEngineerId: Int
  chiefEngineer: Worker
  workers: [Worker]
  hazards: [Hazard]
  documents: [DocumentFile]
  createdAt: DateTime!
  createdBy: String!
  updatedAt: DateTime
  updatedBy: String
  permits: [Permit!]!
  isDeleted: Boolean!
  deletedAt: DateTime
  deletedBy: String
}

type Mutation {
  createWorker(input: CreateWorkerInput!): Worker! @cost(weight: "10")
  createWorkerWithTraining(input: CreateWorkerWithTrainingInput!): Worker!
    @cost(weight: "10")
  updateWorker(input: UpdateWorkerInput!): Worker @cost(weight: "10")
  updateWorkerWithTraining(input: UpdateWorkerWithTrainingInput!): Worker
    @cost(weight: "10")
  deleteWorker(id: Int!): Boolean! @cost(weight: "10")
  createTraining(
    name: String!
    description: String
    startDate: DateTime
    endDate: DateTime
    duration: String
    validityPeriodMonths: Int
    trainingType: String
    trainer: String
    frequency: String
    status: TrainingStatus! = SCHEDULED
  ): Training! @cost(weight: "10")
  updateTraining(
    id: Int!
    name: String
    description: String
    startDate: DateTime
    endDate: DateTime
    duration: String
    validityPeriodMonths: Int
    trainingType: String
    trainer: String
    frequency: String
    status: TrainingStatus
  ): Training @cost(weight: "10")
  deleteTraining(id: Int!): Boolean! @cost(weight: "10")
  assignTrainingToWorkersByTrade(trainingId: Int!, tradeIds: [Int!]!): Int!
    @cost(weight: "10")
  completeTraining(
    workerId: Int!
    trainingId: Int!
    completionDate: DateTime
    score: Decimal
    notes: String
  ): WorkerTrainingHistory! @cost(weight: "10")
  updateTrainingStatuses: Boolean! @cost(weight: "10")
  createTrade(name: String!, description: String, workerIds: [Int!]): Trade!
    @cost(weight: "10")
  updateTrade(
    id: Int!
    name: String
    description: String
    workerIds: [Int!]
  ): Trade @cost(weight: "10")
  deleteTrade(id: Int!): Boolean! @cost(weight: "10")
  createSkill(name: String!, description: String, workerIds: [Int!]): Skill!
    @cost(weight: "10")
  updateSkill(
    id: Int!
    name: String
    description: String
    workerIds: [Int!]
  ): Skill @cost(weight: "10")
  deleteSkill(id: Int!): Boolean! @cost(weight: "10")
  createEquipment(
    name: String!
    description: String
    serialNumber: String
    model: String
    manufacturer: String
    purchaseDate: DateTime
    lastMaintenanceDate: DateTime
    nextMaintenanceDate: DateTime
    location: String
    status: String
    purchasePrice: Decimal
    category: String
  ): Equipment! @cost(weight: "10")
  updateEquipment(
    id: Int!
    name: String
    description: String
    serialNumber: String
    model: String
    manufacturer: String
    purchaseDate: DateTime
    lastMaintenanceDate: DateTime
    nextMaintenanceDate: DateTime
    location: String
    status: String
    purchasePrice: Decimal
    category: String
  ): Equipment @cost(weight: "10")
  deleteEquipment(id: Int!): Boolean! @cost(weight: "10")
  uploadFile(input: FileUploadInput!): FileUploadResponse! @cost(weight: "10")
  presignedUrl(input: PresignedUrlInput!): PresignedUrlResponse!
    @cost(weight: "10")
  deleteFile(fileId: Int!): Boolean! @cost(weight: "10")
  createJob(input: CreateJobInput!): Job! @cost(weight: "10")
  createJobs(inputs: [CreateJobInput!]!): [Job!]! @cost(weight: "10")
  blockJob(input: BlockJobInput!): Job! @cost(weight: "10")
  blockJobs(inputs: [BlockJobInput!]!): [Job!]! @cost(weight: "10")
  reviewJob(input: ReviewJobInput!): Job! @cost(weight: "10")
  reviewJobs(inputs: [ReviewJobInput!]!): [Job!]! @cost(weight: "10")
  approveJob(input: ApproveJobInput!): Job! @cost(weight: "10")
  approveJobs(inputs: [ApproveJobInput!]!): [Job!]! @cost(weight: "10")
  disapproveJob(input: DisapproveJobInput!): Job! @cost(weight: "10")
  disapproveJobs(inputs: [DisapproveJobInput!]!): [Job!]! @cost(weight: "10")
  finishJob(input: FinishJobInput!): Job! @cost(weight: "10")
  finishJobs(inputs: [FinishJobInput!]!): [Job!]! @cost(weight: "10")
  createToolbox(input: CreateToolboxInput!): Toolbox! @cost(weight: "10")
  addAttendees(toolboxId: Int!, workerIds: [Int!]!): Boolean!
    @cost(weight: "10")
  summarizeToolbox(input: SummarizeToolboxInput!): Boolean! @cost(weight: "10")
  addHazard(input: AddHazardInput!): Boolean! @cost(weight: "10")
  addControlMeasure(input: AddControlMeasureInput!): Boolean!
    @cost(weight: "10")
  createGeneralWorkPermit(
    input: CreateGeneralWorkPermitInput!
  ): GeneralWorkPermit! @cost(weight: "10")
  createHotWorkPermit(input: CreateHotWorkPermitInput!): HotWorkPermit!
    @cost(weight: "10")
  approvePermit(permitId: Int!): Permit! @cost(weight: "10")
  disapprovePermit(permitId: Int!): Permit! @cost(weight: "10")
  cancelPermit(permitId: Int!): Permit! @cost(weight: "10")
  returnPermit(input: ReturnPermitInput!): Permit! @cost(weight: "10")
  voidPermit(permitId: Int!): Permit! @cost(weight: "10")
  closePermit(permitId: Int!): Permit! @cost(weight: "10")
  addNewWorkerToPermit(permitId: Int!, workerId: Int!): GeneralWorkPermit!
    @cost(weight: "10")
  renewPermit(permitId: Int!, workerId: Int!): GeneralWorkPermit!
    @cost(weight: "10")
  createExcavationWorkPermit(
    input: CreateExcavationWorkPermitInput!
  ): ExcavationWorkPermit! @cost(weight: "10")
  createWorkAtHeightPermit(
    input: CreateWorkAtHeightPermitInput!
  ): WorkAtHeightPermit! @cost(weight: "10")
  createConfinedSpacePermit(
    input: CreateConfinedSpacePermitInput!
  ): ConfinedSpacePermit! @cost(weight: "10")
}

type Permit {
  id: Int!
  jobId: Int!
  job: Job
  ptwRefNumber: String!
  projectName: String!
  startingDateTime: DateTime!
  endingDateTime: DateTime!
  description: String
  location: String
  hazards: String
  precautionsRequired: String
  ppe: String
  status: PermitStatus!
  daysValid: Int!
  permitType: PermitType!
  permitIssuer: PermitIssuer
  permitReturn: PermitReturn
  signOff: SignOff
  otherPermitsInUse: [Permit]
  documents: [DocumentFile]
  createdAt: DateTime!
  createdBy: String!
  updatedAt: DateTime
  updatedBy: String
  referencedByPermits: [Permit!]!
  isDeleted: Boolean!
  deletedAt: DateTime
  deletedBy: String
}

type PermitIssuer {
  competentPersons: [CompetentPerson]
  authorisedPersons: [AuthorisedPerson]
}

type PermitReturn {
  competentPersons: [CompetentPerson]
  authorisedPersons: [AuthorisedPerson]
}

type PermitWorker {
  workerId: Int!
  designation: String!
  name: String!
  signatureFileId: String!
  signedAt: DateTime!
}

type PresignedUrlResponse {
  success: Boolean!
  errorMessage: String
  url: String
  expiresAt: DateTime
}

type Query {
  allWorkers(where: WorkerFilterInput @cost(weight: "10")): [Worker!]!
    @cost(weight: "10")
  workerById(id: Int!): [Worker!]! @cost(weight: "10")
  allTrainings(where: TrainingFilterInput @cost(weight: "10")): [Training!]!
    @cost(weight: "10")
  trainingById(id: Int!): [Training!]! @cost(weight: "10")
  allTrades: [Trade!]! @cost(weight: "10")
  tradeById(id: Int!): Trade @cost(weight: "10")
  allSkills: [Skill!]! @cost(weight: "10")
  skillById(id: Int!): Skill @cost(weight: "10")
  workerTrainingHistory(workerId: Int!): [WorkerTrainingHistory!]!
    @cost(weight: "10")
  expiringTrainings(daysAhead: Int! = 30): [WorkerTrainingHistory!]!
    @cost(weight: "10")
  expiredTrainings: [WorkerTrainingHistory!]! @cost(weight: "10")
  allEquipment: [Equipment!]! @cost(weight: "10")
  equipmentById(id: Int!): Equipment @cost(weight: "10")
  equipmentByStatus(status: String!): [Equipment!]! @cost(weight: "10")
  equipmentByCategory(category: String!): [Equipment!]! @cost(weight: "10")
  equipmentByLocation(location: String!): [Equipment!]! @cost(weight: "10")
  allJobs(where: JobFilterInput @cost(weight: "10")): [Job!]!
    @cost(weight: "10")
  jobById(id: Int!): [Job!]! @cost(weight: "10")
  requestedJobs: [Job!]! @cost(weight: "10")
  blockedJobs: [Job!]! @cost(weight: "10")
  pendingApprovalJobs: [Job!]! @cost(weight: "10")
  approvedJobs: [Job!]! @cost(weight: "10")
  disapprovedJobs: [Job!]! @cost(weight: "10")
  finishedJobs: [Job!]! @cost(weight: "10")
  jobsByStatus(status: JobStatus!): [Job!]! @cost(weight: "10")
  jobsByWorkerId(workerId: Int!): [Job!]! @cost(weight: "10")
  jobsByChiefEngineerId(chiefEngineerId: Int!): [Job!]! @cost(weight: "10")
  allCategories(where: CategoryFilterInput @cost(weight: "10")): [Category!]!
    @cost(weight: "10")
  categoryById(id: Int!): [Category!]! @cost(weight: "10")
  todaysJobRiskAssessment: [TodaysJobRiskAssessment!]! @cost(weight: "10")
  allToolboxes(where: ToolboxFilterInput @cost(weight: "10")): [Toolbox!]!
    @cost(weight: "10")
  toolboxById(id: Int!): [Toolbox!]! @cost(weight: "10")
  todaysToolbox: Toolbox @cost(weight: "10")
  toolboxRiskAssessment(jobId: Int!): [ToolboxRiskAssessment!]!
    @cost(weight: "10")
  verifySignoff(workerIds: [Int!]!): Boolean! @cost(weight: "10")
  allPermits(where: PermitFilterInput @cost(weight: "10")): [Permit!]!
    @cost(weight: "10")
  permitById(id: Int!): [Permit!]! @cost(weight: "10")
  permitsByJob(jobId: Int!): [Permit!]! @cost(weight: "10")
  permitsByStatus(status: PermitStatus!): [Permit!]! @cost(weight: "10")
  permitsByDate(date: DateTime!): [Permit!]! @cost(weight: "10")
}

type SignOff {
  dateTime: DateTime!
  workers: [PermitWorker]
}

type Skill {
  id: Int!
  name: String!
  description: String
  workers: [Worker]
  createdAt: DateTime!
  createdBy: String!
  updatedAt: DateTime
  updatedBy: String!
}

type TaskObserver {
  workerId: Int!
  name: String!
  signatureFileId: String!
  signedAt: DateTime!
}

type TodaysJobControlMeasure {
  id: Int!
  description: String!
}

type TodaysJobHazard {
  id: Int!
  description: String!
  controlMeasures: [TodaysJobControlMeasure]
}

type TodaysJobRiskAssessment {
  id: Int!
  title: String!
  hazards: [TodaysJobHazard]
}

type Toolbox {
  id: Int!
  date: DateTime!
  status: ToolboxStatus!
  emergencyProcedures: String!
  toolboxTrainingTopics: String!
  closedDate: DateTime
  conductor: ToolboxConductor
  conductedBy: ToolboxConductor
  attendees: [ToolboxAttendee]
  jobs: [Job]
  createdAt: DateTime!
  createdBy: String!
  updatedAt: DateTime
  updatedBy: String
  isDeleted: Boolean!
  deletedAt: DateTime
  deletedBy: String
}

type ToolboxAttendance {
  id: Int!
  toolboxSessionId: Int!
  workerId: Int!
  worker: Worker
  wasPresent: Boolean!
  notes: String
  createdAt: DateTime!
  createdBy: String!
  updatedAt: DateTime
  updatedBy: String
  toolboxSession: ToolboxSession!
}

type ToolboxAttendee {
  workerId: Int!
  name: String!
  designation: String!
  signatureFileId: String!
}

type ToolboxConductor {
  workerId: Int!
  name: String!
  signatureFileId: String!
}

type ToolboxRiskAssessment {
  id: Int!
  title: String!
  hazards: ToolboxRiskAssessmentHazard
}

type ToolboxRiskAssessmentControlMeasure {
  id: Int!
  description: String!
}

type ToolboxRiskAssessmentHazard {
  id: Int!
  description: String!
  controlMeasures: [ToolboxRiskAssessmentControlMeasure]
}

type ToolboxSession {
  id: Int!
  sessionTime: DateTime!
  topic: String!
  conductor: String!
  photoUrl: String!
  notes: String
  attendances: [ToolboxAttendance]
  createdAt: DateTime!
  createdBy: String!
  updatedAt: DateTime
  updatedBy: String
}

type Trade {
  id: Int!
  name: String!
  description: String
  workers: [Worker]
  createdAt: DateTime!
  createdBy: String!
  updatedAt: DateTime
  updatedBy: String!
}

type Training {
  id: Int!
  name: String!
  description: String
  workers: [Worker]
  workerTrainings: [WorkerTraining]
  trainingHistory: [WorkerTrainingHistory]
  duration: String
  parsedDuration: String @cost(weight: "10")
  startDate: DateTime
  endDate: DateTime
  validityPeriodMonths: Int
  trainingType: String
  trainer: String
  frequency: String
  status: TrainingStatus!
  createdAt: DateTime!
  createdBy: String!
  updatedAt: DateTime
  updatedBy: String!
  isDeleted: Boolean!
  deletedAt: DateTime
  deletedBy: String
}

type WorkAreaInspection {
  name: String!
  signatureFileId: String!
  signedAt: DateTime!
  comments: String
}

type WorkAtHeightPermit {
  modeOfAccessToBeUsed: String
  inspections: String
  inspectionAuthorization: InspectionAuthorization
  id: Int!
  jobId: Int!
  job: Job!
  ptwRefNumber: String!
  projectName: String!
  startingDateTime: DateTime!
  endingDateTime: DateTime!
  description: String!
  location: String!
  hazards: String!
  precautionsRequired: String!
  ppe: String!
  status: PermitStatus!
  daysValid: Int!
  permitIssuer: PermitIssuer!
  permitReturn: PermitReturn
  signOff: SignOff!
  otherPermitsInUse: [Permit!]!
  referencedByPermits: [Permit!]!
  documents: [DocumentFile!]!
  permitType: PermitType!
  createdAt: DateTime!
  createdBy: String!
  updatedAt: DateTime
  updatedBy: String!
  isDeleted: Boolean!
  deletedAt: DateTime
  deletedBy: String
}

type Worker {
  id: Int!
  name: String!
  company: String!
  phoneNumber: String!
  nationalId: String!
  gender: String!
  dateOfBirth: Date
  inductionDate: DateTime
  medicalCheckDate: DateTime
  mpesaNumber: String
  email: String
  profilePictureFileId: Int
  profilePictureFile: FileMetadata
  signatureFileId: Int
  signatureFile: FileMetadata
  documentFiles: [DocumentFile]
  "URL to the worker's profile picture"
  profilePictureUrl: String @cost(weight: "10")
  "URL to the worker's signature"
  signatureUrl: String @cost(weight: "10")
  "List of document files with names and URLs"
  documentUrls: [DocumentFile] @cost(weight: "10")
  age: Int
  trainingsCompleted: Int!
  manHours: Int!
  rating: Float!
  trainings: [Training]
  workerTrainings: [WorkerTraining]
  trades: [Trade]
  skills: [Skill]
  trainingHistory: [WorkerTrainingHistory]
  incidents: [Incident]
  createdAt: DateTime!
  createdBy: String!
  updatedAt: DateTime
  updatedBy: String!
  isDeleted: Boolean!
  deletedAt: DateTime
  deletedBy: String
}

type WorkerAttendance {
  id: Int!
  workerId: Int!
  worker: Worker
  checkInTime: DateTime!
  checkOutTime: DateTime
  status: String!
  notes: String
  isVerifiedByHikvision: Boolean!
  createdAt: DateTime!
  createdBy: String!
  updatedAt: DateTime
  updatedBy: String
}

type WorkerTraining {
  workerId: Int!
  trainingId: Int!
  worker: Worker
  training: Training
  notes: String
  assignedDate: DateTime!
  "List of document file IDs associated with this worker-training relationship"
  documentFileIds: [Int] @cost(weight: "10")
  "List of document URLs for this worker-training relationship"
  documentUrls: [String] @cost(weight: "10")
  createdAt: DateTime!
  createdBy: String!
  updatedAt: DateTime
  updatedBy: String!
  documentFiles: [DocumentFile!]!
  isDeleted: Boolean!
  deletedAt: DateTime
  deletedBy: String
}

type WorkerTrainingHistory {
  id: Int!
  workerId: Int!
  trainingId: Int!
  worker: Worker!
  training: Training!
  completionDate: DateTime!
  expiryDate: DateTime
  status: TrainingStatus!
  notes: String
  certificateUrl: String
  score: Float
  isExpired: Boolean!
  isExpiringSoon: Boolean!
  daysUntilExpiry: Int
  createdAt: DateTime!
  createdBy: String!
  updatedAt: DateTime
  updatedBy: String!
}

input AddControlMeasureInput {
  hazardId: Int!
  description: String!
}

input AddHazardInput {
  jobId: Int!
  description: String!
  controlMeasures: [String!]!
}

input AllowedFileTypeOperationFilterInput {
  eq: AllowedFileType @cost(weight: "10")
  neq: AllowedFileType @cost(weight: "10")
  in: [AllowedFileType!] @cost(weight: "10")
  nin: [AllowedFileType!] @cost(weight: "10")
}

input ApproveJobInput {
  jobId: Int!
  approvedById: Int!
}

input AtmosphericReadingInput {
  oxygen: String!
  explosive: String!
  toxic: String!
  co2: String!
}

input AuthorisedPersonFilterInput {
  and: [AuthorisedPersonFilterInput!]
  or: [AuthorisedPersonFilterInput!]
  workerId: IntOperationFilterInput
  name: StringOperationFilterInput
  signatureFileId: StringOperationFilterInput
  signedAt: DateTimeOperationFilterInput
}

input AuthorisedPersonInput {
  workerId: Int!
  name: String!
  signatureFileId: String!
  signedAt: DateTime!
}

input BlockJobInput {
  jobId: Int!
  blockedById: Int!
}

input BooleanOperationFilterInput {
  eq: Boolean @cost(weight: "10")
  neq: Boolean @cost(weight: "10")
}

input CategoryFilterInput {
  and: [CategoryFilterInput!]
  or: [CategoryFilterInput!]
  id: IntOperationFilterInput
  description: StringOperationFilterInput
  jobs: ListFilterInputTypeOfJobFilterInput
  createdAt: DateTimeOperationFilterInput
  createdBy: StringOperationFilterInput
  updatedAt: DateTimeOperationFilterInput
  updatedBy: StringOperationFilterInput
  isDeleted: BooleanOperationFilterInput
  deletedAt: DateTimeOperationFilterInput
  deletedBy: StringOperationFilterInput
}

input CompetentPersonFilterInput {
  and: [CompetentPersonFilterInput!]
  or: [CompetentPersonFilterInput!]
  workerId: IntOperationFilterInput
  name: StringOperationFilterInput
  signatureFileId: StringOperationFilterInput
  signedAt: DateTimeOperationFilterInput
}

input CompetentPersonInput {
  workerId: Int!
  name: String!
  signatureFileId: String!
  signedAt: DateTime!
}

input ControlMeasureFilterInput {
  and: [ControlMeasureFilterInput!]
  or: [ControlMeasureFilterInput!]
  id: IntOperationFilterInput
  description: StringOperationFilterInput
  closed: BooleanOperationFilterInput
  hazardId: IntOperationFilterInput
  hazard: HazardFilterInput
  createdAt: DateTimeOperationFilterInput
  createdBy: StringOperationFilterInput
  updatedAt: DateTimeOperationFilterInput
  updatedBy: StringOperationFilterInput
  isDeleted: BooleanOperationFilterInput
  deletedAt: DateTimeOperationFilterInput
  deletedBy: StringOperationFilterInput
}

input CreateConfinedSpacePermitInput {
  jobId: Int!
  ptwRefNumber: String!
  projectName: String!
  startingDateTime: DateTime!
  endingDateTime: DateTime!
  description: String!
  location: String!
  hazards: String!
  precautionsRequired: String!
  ppe: String!
  workersHaveBeenTrained: Boolean!
  nameOfTrainingOrganization: String!
  topReading: AtmosphericReadingInput!
  midReading: AtmosphericReadingInput!
  bottomReading: AtmosphericReadingInput!
  emergencyGuidelines: String!
  taskObserver: TaskObserverInput!
  permitIssuer: PermitIssuerInput!
  signOff: SignOffInput!
}

input CreateExcavationWorkPermitInput {
  jobId: Int!
  ptwRefNumber: String!
  projectName: String!
  startingDateTime: DateTime!
  endingDateTime: DateTime!
  description: String!
  location: String!
  hazards: String!
  precautionsRequired: String!
  ppe: String!
  depthOfExcavation: String!
  protectionSystems: String!
  listOfEquipmentToBeUsed: String!
  inspections: String!
  inspectionAuthorization: InspectionAuthorizationInput!
  permitIssuer: PermitIssuerInput!
  signOff: SignOffInput!
}

input CreateGeneralWorkPermitInput {
  jobId: Int!
  ptwRefNumber: String!
  projectName: String!
  startingDateTime: DateTime!
  endingDateTime: DateTime!
  description: String!
  location: String!
  hazards: String!
  precautionsRequired: String!
  ppe: String!
  isolation: String!
  permitIssuer: PermitIssuerInput!
  signOff: SignOffInput!
}

input CreateHotWorkPermitInput {
  jobId: Int!
  ptwRefNumber: String!
  projectName: String!
  startingDateTime: DateTime!
  endingDateTime: DateTime!
  description: String!
  location: String!
  hazards: String!
  precautionsRequired: String!
  ppe: String!
  natureOfWork: String!
  fireExtinguishers: String!
  fireSafetySupervisor: FireSafetySupervisorInput!
  permitIssuer: PermitIssuerInput!
  signOff: SignOffInput!
}

input CreateJobInput {
  title: String!
  workerIds: [Int!]!
  chiefEngineerId: Int!
  timeForCompletion: String!
  startDate: DateTime
  description: String
}

input CreateToolboxExistingControlMeasureInput {
  id: Int!
  description: String!
}

input CreateToolboxExistingHazardInput {
  id: Int!
  description: String!
  existingControlMeasures: [CreateToolboxExistingControlMeasureInput!]!
  newControlMeasures: [CreateToolboxNewControlMeasureInput!]!
}

input CreateToolboxInput {
  conductorId: Int!
  jobs: [CreateToolboxJobInput!]!
  emergencyProcedures: String!
  toolboxTrainingTopics: String!
}

input CreateToolboxJobInput {
  jobId: Int!
  existingHazards: [CreateToolboxExistingHazardInput!]!
  newHazards: [CreateToolboxNewHazardInput!]!
}

input CreateToolboxNewControlMeasureInput {
  description: String!
}

input CreateToolboxNewHazardInput {
  description: String!
  controlMeasures: [CreateToolboxNewControlMeasureInput!]!
}

input CreateWorkAtHeightPermitInput {
  jobId: Int!
  ptwRefNumber: String!
  projectName: String!
  startingDateTime: DateTime!
  endingDateTime: DateTime!
  description: String!
  location: String!
  hazards: String!
  precautionsRequired: String!
  ppe: String!
  modeOfAccessToBeUsed: String!
  inspections: String!
  inspectionAuthorization: InspectionAuthorizationInput!
  permitIssuer: PermitIssuerInput!
  signOff: SignOffInput!
}

input CreateWorkerInput {
  name: String!
  company: String!
  nationalId: String!
  gender: String!
  phoneNumber: String!
  dateOfBirth: Date
  trainingIds: [Int!]
  tradeIds: [Int!]
  skillIds: [Int!]
  mpesaNumber: String
  email: String
  inductionDate: DateTime
  medicalCheckDate: DateTime
  profilePicture: Upload
  signature: Upload
  documents: [DocumentFileInput!]
}

input CreateWorkerWithTrainingInput {
  name: String!
  company: String!
  nationalId: String!
  gender: String!
  phoneNumber: String!
  dateOfBirth: Date
  trainings: [WorkerTrainingInput!]
  tradeIds: [Int!]
  skillIds: [Int!]
  mpesaNumber: String
  email: String
  inductionDate: DateTime
  medicalCheckDate: DateTime
  profilePicture: Upload!
  signature: Upload
  documents: [DocumentFileInput!]
}

input DateOperationFilterInput {
  eq: Date @cost(weight: "10")
  neq: Date @cost(weight: "10")
  in: [Date] @cost(weight: "10")
  nin: [Date] @cost(weight: "10")
  gt: Date @cost(weight: "10")
  ngt: Date @cost(weight: "10")
  gte: Date @cost(weight: "10")
  ngte: Date @cost(weight: "10")
  lt: Date @cost(weight: "10")
  nlt: Date @cost(weight: "10")
  lte: Date @cost(weight: "10")
  nlte: Date @cost(weight: "10")
}

input DateTimeOperationFilterInput {
  eq: DateTime @cost(weight: "10")
  neq: DateTime @cost(weight: "10")
  in: [DateTime] @cost(weight: "10")
  nin: [DateTime] @cost(weight: "10")
  gt: DateTime @cost(weight: "10")
  ngt: DateTime @cost(weight: "10")
  gte: DateTime @cost(weight: "10")
  ngte: DateTime @cost(weight: "10")
  lt: DateTime @cost(weight: "10")
  nlt: DateTime @cost(weight: "10")
  lte: DateTime @cost(weight: "10")
  nlte: DateTime @cost(weight: "10")
}

input DecimalOperationFilterInput {
  eq: Decimal @cost(weight: "10")
  neq: Decimal @cost(weight: "10")
  in: [Decimal] @cost(weight: "10")
  nin: [Decimal] @cost(weight: "10")
  gt: Decimal @cost(weight: "10")
  ngt: Decimal @cost(weight: "10")
  gte: Decimal @cost(weight: "10")
  ngte: Decimal @cost(weight: "10")
  lt: Decimal @cost(weight: "10")
  nlt: Decimal @cost(weight: "10")
  lte: Decimal @cost(weight: "10")
  nlte: Decimal @cost(weight: "10")
}

input DisapproveJobInput {
  jobId: Int!
  disapprovedById: Int!
}

input DocumentFileFilterInput {
  and: [DocumentFileFilterInput!]
  or: [DocumentFileFilterInput!]
  id: IntOperationFilterInput
  name: StringOperationFilterInput
  fileMetadataId: IntOperationFilterInput
  fileMetadata: FileMetadataFilterInput
  url: StringOperationFilterInput
  createdAt: DateTimeOperationFilterInput
  createdBy: StringOperationFilterInput
  updatedAt: DateTimeOperationFilterInput
  updatedBy: StringOperationFilterInput
  isDeleted: BooleanOperationFilterInput
  deletedAt: DateTimeOperationFilterInput
  deletedBy: StringOperationFilterInput
}

input DocumentFileInput {
  name: String!
  file: Upload!
  description: String
  folderPath: String
  isPublic: Boolean!
  expiresAt: DateTime
  additionalMetadata: String
}

input FileMetadataFilterInput {
  and: [FileMetadataFilterInput!]
  or: [FileMetadataFilterInput!]
  id: IntOperationFilterInput
  fileName: StringOperationFilterInput
  contentType: StringOperationFilterInput
  size: LongOperationFilterInput
  description: StringOperationFilterInput
  bucketName: StringOperationFilterInput
  objectKey: StringOperationFilterInput
  eTag: StringOperationFilterInput
  version: StringOperationFilterInput
  fileType: AllowedFileTypeOperationFilterInput
  folderPath: StringOperationFilterInput
  isPublic: BooleanOperationFilterInput
  expiresAt: DateTimeOperationFilterInput
  additionalMetadata: StringOperationFilterInput
  url: StringOperationFilterInput
  createdAt: DateTimeOperationFilterInput
  createdBy: StringOperationFilterInput
  updatedAt: DateTimeOperationFilterInput
  updatedBy: StringOperationFilterInput
  isDeleted: BooleanOperationFilterInput
  deletedAt: DateTimeOperationFilterInput
  deletedBy: StringOperationFilterInput
  workersWithProfilePicture: ListFilterInputTypeOfWorkerFilterInput
  toolboxSessions: ListFilterInputTypeOfToolboxSessionFilterInput
}

input FileUploadInput {
  file: Upload!
  bucketName: String!
  folderPath: String
  description: String
  isPublic: Boolean!
  expiresAt: DateTime
  additionalMetadata: String
}

input FinishJobInput {
  jobId: Int!
  finishedById: Int!
}

input FireSafetySupervisorInput {
  workerId: Int!
  name: String!
  signatureFileId: String!
  signedAt: DateTime!
}

input FloatOperationFilterInput {
  eq: Float @cost(weight: "10")
  neq: Float @cost(weight: "10")
  in: [Float] @cost(weight: "10")
  nin: [Float] @cost(weight: "10")
  gt: Float @cost(weight: "10")
  ngt: Float @cost(weight: "10")
  gte: Float @cost(weight: "10")
  ngte: Float @cost(weight: "10")
  lt: Float @cost(weight: "10")
  nlt: Float @cost(weight: "10")
  lte: Float @cost(weight: "10")
  nlte: Float @cost(weight: "10")
}

input HazardFilterInput {
  and: [HazardFilterInput!]
  or: [HazardFilterInput!]
  id: IntOperationFilterInput
  description: StringOperationFilterInput
  jobId: IntOperationFilterInput
  job: JobFilterInput
  controlMeasures: ListFilterInputTypeOfControlMeasureFilterInput
  createdAt: DateTimeOperationFilterInput
  createdBy: StringOperationFilterInput
  updatedAt: DateTimeOperationFilterInput
  updatedBy: StringOperationFilterInput
  isDeleted: BooleanOperationFilterInput
  deletedAt: DateTimeOperationFilterInput
  deletedBy: StringOperationFilterInput
}

input HazardInput {
  description: String!
  controlMeasures: [String!]!
}

input IncidentFilterInput {
  and: [IncidentFilterInput!]
  or: [IncidentFilterInput!]
  id: IntOperationFilterInput
  title: StringOperationFilterInput
  description: StringOperationFilterInput
  incidentDate: DateTimeOperationFilterInput
  location: StringOperationFilterInput
  status: IncidentStatusOperationFilterInput
  reportedBy: StringOperationFilterInput
  investigatedBy: StringOperationFilterInput
  resolution: StringOperationFilterInput
  resolvedDate: DateTimeOperationFilterInput
  workers: ListFilterInputTypeOfWorkerFilterInput
  createdAt: DateTimeOperationFilterInput
  createdBy: StringOperationFilterInput
  updatedAt: DateTimeOperationFilterInput
  updatedBy: StringOperationFilterInput
  isDeleted: BooleanOperationFilterInput
  deletedAt: DateTimeOperationFilterInput
  deletedBy: StringOperationFilterInput
}

input IncidentStatusOperationFilterInput {
  eq: IncidentStatus @cost(weight: "10")
  neq: IncidentStatus @cost(weight: "10")
  in: [IncidentStatus!] @cost(weight: "10")
  nin: [IncidentStatus!] @cost(weight: "10")
}

input InspectionAuthorizationInput {
  nameOfInspector: String!
  designation: String!
  dateOfInspection: DateTime!
  comments: String!
}

input IntOperationFilterInput {
  eq: Int @cost(weight: "10")
  neq: Int @cost(weight: "10")
  in: [Int] @cost(weight: "10")
  nin: [Int] @cost(weight: "10")
  gt: Int @cost(weight: "10")
  ngt: Int @cost(weight: "10")
  gte: Int @cost(weight: "10")
  ngte: Int @cost(weight: "10")
  lt: Int @cost(weight: "10")
  nlt: Int @cost(weight: "10")
  lte: Int @cost(weight: "10")
  nlte: Int @cost(weight: "10")
}

input JobFilterInput {
  and: [JobFilterInput!]
  or: [JobFilterInput!]
  id: IntOperationFilterInput
  title: StringOperationFilterInput
  description: StringOperationFilterInput
  status: JobStatusOperationFilterInput
  requiredPermits: ListPermitTypeOperationFilterInput
  timeForCompletion: StringOperationFilterInput
  startDate: DateTimeOperationFilterInput
  dueDate: DateTimeOperationFilterInput
  categoryId: IntOperationFilterInput
  category: CategoryFilterInput
  requestedById: IntOperationFilterInput
  requestedBy: WorkerFilterInput
  requestedDate: DateTimeOperationFilterInput
  blockedById: IntOperationFilterInput
  blockedBy: WorkerFilterInput
  blockedDate: DateTimeOperationFilterInput
  reviewedById: IntOperationFilterInput
  reviewedBy: WorkerFilterInput
  reviewedDate: DateTimeOperationFilterInput
  approvedById: IntOperationFilterInput
  approvedBy: WorkerFilterInput
  approvedDate: DateTimeOperationFilterInput
  finishedById: IntOperationFilterInput
  finishedBy: WorkerFilterInput
  finishedDate: DateTimeOperationFilterInput
  chiefEngineerId: IntOperationFilterInput
  chiefEngineer: WorkerFilterInput
  workers: ListFilterInputTypeOfWorkerFilterInput
  hazards: ListFilterInputTypeOfHazardFilterInput
  documents: ListFilterInputTypeOfDocumentFileFilterInput
  permits: ListFilterInputTypeOfPermitFilterInput
  createdAt: DateTimeOperationFilterInput
  createdBy: StringOperationFilterInput
  updatedAt: DateTimeOperationFilterInput
  updatedBy: StringOperationFilterInput
  isDeleted: BooleanOperationFilterInput
  deletedAt: DateTimeOperationFilterInput
  deletedBy: StringOperationFilterInput
  calculatedDueDate: DateTimeOperationFilterInput
}

input JobStatusOperationFilterInput {
  eq: JobStatus @cost(weight: "10")
  neq: JobStatus @cost(weight: "10")
  in: [JobStatus!] @cost(weight: "10")
  nin: [JobStatus!] @cost(weight: "10")
}

input ListFilterInputTypeOfAuthorisedPersonFilterInput {
  all: AuthorisedPersonFilterInput @cost(weight: "10")
  none: AuthorisedPersonFilterInput @cost(weight: "10")
  some: AuthorisedPersonFilterInput @cost(weight: "10")
  any: Boolean @cost(weight: "10")
}

input ListFilterInputTypeOfCompetentPersonFilterInput {
  all: CompetentPersonFilterInput @cost(weight: "10")
  none: CompetentPersonFilterInput @cost(weight: "10")
  some: CompetentPersonFilterInput @cost(weight: "10")
  any: Boolean @cost(weight: "10")
}

input ListFilterInputTypeOfControlMeasureFilterInput {
  all: ControlMeasureFilterInput @cost(weight: "10")
  none: ControlMeasureFilterInput @cost(weight: "10")
  some: ControlMeasureFilterInput @cost(weight: "10")
  any: Boolean @cost(weight: "10")
}

input ListFilterInputTypeOfDocumentFileFilterInput {
  all: DocumentFileFilterInput @cost(weight: "10")
  none: DocumentFileFilterInput @cost(weight: "10")
  some: DocumentFileFilterInput @cost(weight: "10")
  any: Boolean @cost(weight: "10")
}

input ListFilterInputTypeOfHazardFilterInput {
  all: HazardFilterInput @cost(weight: "10")
  none: HazardFilterInput @cost(weight: "10")
  some: HazardFilterInput @cost(weight: "10")
  any: Boolean @cost(weight: "10")
}

input ListFilterInputTypeOfIncidentFilterInput {
  all: IncidentFilterInput @cost(weight: "10")
  none: IncidentFilterInput @cost(weight: "10")
  some: IncidentFilterInput @cost(weight: "10")
  any: Boolean @cost(weight: "10")
}

input ListFilterInputTypeOfJobFilterInput {
  all: JobFilterInput @cost(weight: "10")
  none: JobFilterInput @cost(weight: "10")
  some: JobFilterInput @cost(weight: "10")
  any: Boolean @cost(weight: "10")
}

input ListFilterInputTypeOfPermitFilterInput {
  all: PermitFilterInput @cost(weight: "10")
  none: PermitFilterInput @cost(weight: "10")
  some: PermitFilterInput @cost(weight: "10")
  any: Boolean @cost(weight: "10")
}

input ListFilterInputTypeOfPermitWorkerFilterInput {
  all: PermitWorkerFilterInput @cost(weight: "10")
  none: PermitWorkerFilterInput @cost(weight: "10")
  some: PermitWorkerFilterInput @cost(weight: "10")
  any: Boolean @cost(weight: "10")
}

input ListFilterInputTypeOfSkillFilterInput {
  all: SkillFilterInput @cost(weight: "10")
  none: SkillFilterInput @cost(weight: "10")
  some: SkillFilterInput @cost(weight: "10")
  any: Boolean @cost(weight: "10")
}

input ListFilterInputTypeOfToolboxAttendanceFilterInput {
  all: ToolboxAttendanceFilterInput @cost(weight: "10")
  none: ToolboxAttendanceFilterInput @cost(weight: "10")
  some: ToolboxAttendanceFilterInput @cost(weight: "10")
  any: Boolean @cost(weight: "10")
}

input ListFilterInputTypeOfToolboxAttendeeFilterInput {
  all: ToolboxAttendeeFilterInput @cost(weight: "10")
  none: ToolboxAttendeeFilterInput @cost(weight: "10")
  some: ToolboxAttendeeFilterInput @cost(weight: "10")
  any: Boolean @cost(weight: "10")
}

input ListFilterInputTypeOfToolboxSessionFilterInput {
  all: ToolboxSessionFilterInput @cost(weight: "10")
  none: ToolboxSessionFilterInput @cost(weight: "10")
  some: ToolboxSessionFilterInput @cost(weight: "10")
  any: Boolean @cost(weight: "10")
}

input ListFilterInputTypeOfTradeFilterInput {
  all: TradeFilterInput @cost(weight: "10")
  none: TradeFilterInput @cost(weight: "10")
  some: TradeFilterInput @cost(weight: "10")
  any: Boolean @cost(weight: "10")
}

input ListFilterInputTypeOfTrainingFilterInput {
  all: TrainingFilterInput @cost(weight: "10")
  none: TrainingFilterInput @cost(weight: "10")
  some: TrainingFilterInput @cost(weight: "10")
  any: Boolean @cost(weight: "10")
}

input ListFilterInputTypeOfWorkerFilterInput {
  all: WorkerFilterInput @cost(weight: "10")
  none: WorkerFilterInput @cost(weight: "10")
  some: WorkerFilterInput @cost(weight: "10")
  any: Boolean @cost(weight: "10")
}

input ListFilterInputTypeOfWorkerTrainingFilterInput {
  all: WorkerTrainingFilterInput @cost(weight: "10")
  none: WorkerTrainingFilterInput @cost(weight: "10")
  some: WorkerTrainingFilterInput @cost(weight: "10")
  any: Boolean @cost(weight: "10")
}

input ListFilterInputTypeOfWorkerTrainingHistoryFilterInput {
  all: WorkerTrainingHistoryFilterInput @cost(weight: "10")
  none: WorkerTrainingHistoryFilterInput @cost(weight: "10")
  some: WorkerTrainingHistoryFilterInput @cost(weight: "10")
  any: Boolean @cost(weight: "10")
}

input ListPermitTypeOperationFilterInput {
  all: PermitTypeOperationFilterInput @cost(weight: "10")
  none: PermitTypeOperationFilterInput @cost(weight: "10")
  some: PermitTypeOperationFilterInput @cost(weight: "10")
  any: Boolean @cost(weight: "10")
}

input LongOperationFilterInput {
  eq: Long @cost(weight: "10")
  neq: Long @cost(weight: "10")
  in: [Long] @cost(weight: "10")
  nin: [Long] @cost(weight: "10")
  gt: Long @cost(weight: "10")
  ngt: Long @cost(weight: "10")
  gte: Long @cost(weight: "10")
  ngte: Long @cost(weight: "10")
  lt: Long @cost(weight: "10")
  nlt: Long @cost(weight: "10")
  lte: Long @cost(weight: "10")
  nlte: Long @cost(weight: "10")
}

input PermitFilterInput {
  and: [PermitFilterInput!]
  or: [PermitFilterInput!]
  id: IntOperationFilterInput
  jobId: IntOperationFilterInput
  job: JobFilterInput
  ptwRefNumber: StringOperationFilterInput
  projectName: StringOperationFilterInput
  startingDateTime: DateTimeOperationFilterInput
  endingDateTime: DateTimeOperationFilterInput
  description: StringOperationFilterInput
  location: StringOperationFilterInput
  hazards: StringOperationFilterInput
  precautionsRequired: StringOperationFilterInput
  ppe: StringOperationFilterInput
  status: PermitStatusOperationFilterInput
  daysValid: IntOperationFilterInput
  permitIssuer: PermitIssuerFilterInput
  permitReturn: PermitReturnFilterInput
  signOff: SignOffFilterInput
  otherPermitsInUse: ListFilterInputTypeOfPermitFilterInput
  referencedByPermits: ListFilterInputTypeOfPermitFilterInput
  documents: ListFilterInputTypeOfDocumentFileFilterInput
  permitType: PermitTypeOperationFilterInput
  createdAt: DateTimeOperationFilterInput
  createdBy: StringOperationFilterInput
  updatedAt: DateTimeOperationFilterInput
  updatedBy: StringOperationFilterInput
  isDeleted: BooleanOperationFilterInput
  deletedAt: DateTimeOperationFilterInput
  deletedBy: StringOperationFilterInput
}

input PermitIssuerFilterInput {
  and: [PermitIssuerFilterInput!]
  or: [PermitIssuerFilterInput!]
  competentPersons: ListFilterInputTypeOfCompetentPersonFilterInput
  authorisedPersons: ListFilterInputTypeOfAuthorisedPersonFilterInput
}

input PermitIssuerInput {
  competentPersons: [CompetentPersonInput!]!
  authorisedPersons: [AuthorisedPersonInput!]!
}

input PermitReturnFilterInput {
  and: [PermitReturnFilterInput!]
  or: [PermitReturnFilterInput!]
  competentPersons: ListFilterInputTypeOfCompetentPersonFilterInput
  authorisedPersons: ListFilterInputTypeOfAuthorisedPersonFilterInput
}

input PermitReturnInput {
  competentPersons: [CompetentPersonInput!]!
  authorisedPersons: [AuthorisedPersonInput!]!
}

input PermitStatusOperationFilterInput {
  eq: PermitStatus @cost(weight: "10")
  neq: PermitStatus @cost(weight: "10")
  in: [PermitStatus!] @cost(weight: "10")
  nin: [PermitStatus!] @cost(weight: "10")
}

input PermitTypeOperationFilterInput {
  eq: PermitType @cost(weight: "10")
  neq: PermitType @cost(weight: "10")
  in: [PermitType!] @cost(weight: "10")
  nin: [PermitType!] @cost(weight: "10")
}

input PermitWorkerFilterInput {
  and: [PermitWorkerFilterInput!]
  or: [PermitWorkerFilterInput!]
  workerId: IntOperationFilterInput
  designation: StringOperationFilterInput
  name: StringOperationFilterInput
  signatureFileId: StringOperationFilterInput
  signedAt: DateTimeOperationFilterInput
}

input PermitWorkerInput {
  workerId: Int!
  designation: String!
  name: String!
  signatureFileId: String!
  signedAt: DateTime!
}

input PresignedUrlInput {
  fileId: Int!
  expirationMinutes: Int!
}

input ReturnPermitInput {
  permitId: Int!
  permitReturn: PermitReturnInput!
}

input ReviewJobInput {
  jobId: Int!
  reviewedById: Int!
  hazards: [HazardInput!]!
  documents: [DocumentFileInput!]
  requiredPermits: [PermitType!]!
}

input SignOffFilterInput {
  and: [SignOffFilterInput!]
  or: [SignOffFilterInput!]
  dateTime: DateTimeOperationFilterInput
  workers: ListFilterInputTypeOfPermitWorkerFilterInput
}

input SignOffInput {
  dateTime: DateTime!
  workers: [PermitWorkerInput!]!
}

input SkillFilterInput {
  and: [SkillFilterInput!]
  or: [SkillFilterInput!]
  id: IntOperationFilterInput
  name: StringOperationFilterInput
  description: StringOperationFilterInput
  workers: ListFilterInputTypeOfWorkerFilterInput
  createdAt: DateTimeOperationFilterInput
  createdBy: StringOperationFilterInput
  updatedAt: DateTimeOperationFilterInput
  updatedBy: StringOperationFilterInput
}

input StringOperationFilterInput {
  and: [StringOperationFilterInput!]
  or: [StringOperationFilterInput!]
  eq: String @cost(weight: "10")
  neq: String @cost(weight: "10")
  contains: String @cost(weight: "20")
  ncontains: String @cost(weight: "20")
  in: [String] @cost(weight: "10")
  nin: [String] @cost(weight: "10")
  startsWith: String @cost(weight: "20")
  nstartsWith: String @cost(weight: "20")
  endsWith: String @cost(weight: "20")
  nendsWith: String @cost(weight: "20")
}

input SummarizeToolboxInput {
  toolboxId: Int!
  jobs: [CreateToolboxJobInput!]!
}

input TaskObserverInput {
  workerId: Int!
  name: String!
  signatureFileId: String!
  signedAt: DateTime!
}

input ToolboxAttendanceFilterInput {
  and: [ToolboxAttendanceFilterInput!]
  or: [ToolboxAttendanceFilterInput!]
  id: IntOperationFilterInput
  toolboxSessionId: IntOperationFilterInput
  toolboxSession: ToolboxSessionFilterInput
  workerId: IntOperationFilterInput
  worker: WorkerFilterInput
  wasPresent: BooleanOperationFilterInput
  notes: StringOperationFilterInput
  createdAt: DateTimeOperationFilterInput
  createdBy: StringOperationFilterInput
  updatedAt: DateTimeOperationFilterInput
  updatedBy: StringOperationFilterInput
}

input ToolboxAttendeeFilterInput {
  and: [ToolboxAttendeeFilterInput!]
  or: [ToolboxAttendeeFilterInput!]
  workerId: IntOperationFilterInput
  name: StringOperationFilterInput
  designation: StringOperationFilterInput
  signatureFileId: StringOperationFilterInput
}

input ToolboxConductorFilterInput {
  and: [ToolboxConductorFilterInput!]
  or: [ToolboxConductorFilterInput!]
  workerId: IntOperationFilterInput
  name: StringOperationFilterInput
  signatureFileId: StringOperationFilterInput
}

input ToolboxFilterInput {
  and: [ToolboxFilterInput!]
  or: [ToolboxFilterInput!]
  id: IntOperationFilterInput
  date: DateTimeOperationFilterInput
  status: ToolboxStatusOperationFilterInput
  emergencyProcedures: StringOperationFilterInput
  toolboxTrainingTopics: StringOperationFilterInput
  closedDate: DateTimeOperationFilterInput
  conductor: ToolboxConductorFilterInput
  conductedBy: ToolboxConductorFilterInput
  attendees: ListFilterInputTypeOfToolboxAttendeeFilterInput
  jobs: ListFilterInputTypeOfJobFilterInput
  createdAt: DateTimeOperationFilterInput
  createdBy: StringOperationFilterInput
  updatedAt: DateTimeOperationFilterInput
  updatedBy: StringOperationFilterInput
  isDeleted: BooleanOperationFilterInput
  deletedAt: DateTimeOperationFilterInput
  deletedBy: StringOperationFilterInput
}

input ToolboxSessionFilterInput {
  and: [ToolboxSessionFilterInput!]
  or: [ToolboxSessionFilterInput!]
  id: IntOperationFilterInput
  sessionTime: DateTimeOperationFilterInput
  topic: StringOperationFilterInput
  conductor: StringOperationFilterInput
  photoUrl: StringOperationFilterInput
  notes: StringOperationFilterInput
  attendances: ListFilterInputTypeOfToolboxAttendanceFilterInput
  createdAt: DateTimeOperationFilterInput
  createdBy: StringOperationFilterInput
  updatedAt: DateTimeOperationFilterInput
  updatedBy: StringOperationFilterInput
}

input ToolboxStatusOperationFilterInput {
  eq: ToolboxStatus @cost(weight: "10")
  neq: ToolboxStatus @cost(weight: "10")
  in: [ToolboxStatus!] @cost(weight: "10")
  nin: [ToolboxStatus!] @cost(weight: "10")
}

input TradeFilterInput {
  and: [TradeFilterInput!]
  or: [TradeFilterInput!]
  id: IntOperationFilterInput
  name: StringOperationFilterInput
  description: StringOperationFilterInput
  workers: ListFilterInputTypeOfWorkerFilterInput
  createdAt: DateTimeOperationFilterInput
  createdBy: StringOperationFilterInput
  updatedAt: DateTimeOperationFilterInput
  updatedBy: StringOperationFilterInput
}

input TrainingDurationFilterInput {
  and: [TrainingDurationFilterInput!]
  or: [TrainingDurationFilterInput!]
  years: IntOperationFilterInput
  months: IntOperationFilterInput
  days: IntOperationFilterInput
  hours: IntOperationFilterInput
  minutes: IntOperationFilterInput
}

input TrainingFilterInput {
  and: [TrainingFilterInput!]
  or: [TrainingFilterInput!]
  id: IntOperationFilterInput
  name: StringOperationFilterInput
  description: StringOperationFilterInput
  workers: ListFilterInputTypeOfWorkerFilterInput
  workerTrainings: ListFilterInputTypeOfWorkerTrainingFilterInput
  trainingHistory: ListFilterInputTypeOfWorkerTrainingHistoryFilterInput
  startDate: DateTimeOperationFilterInput
  endDate: DateTimeOperationFilterInput
  duration: StringOperationFilterInput
  validityPeriodMonths: IntOperationFilterInput
  trainingType: StringOperationFilterInput
  trainer: StringOperationFilterInput
  frequency: StringOperationFilterInput
  status: TrainingStatusOperationFilterInput
  createdAt: DateTimeOperationFilterInput
  createdBy: StringOperationFilterInput
  updatedAt: DateTimeOperationFilterInput
  updatedBy: StringOperationFilterInput
  isDeleted: BooleanOperationFilterInput
  deletedAt: DateTimeOperationFilterInput
  deletedBy: StringOperationFilterInput
  parsedDuration: TrainingDurationFilterInput
}

input TrainingStatusOperationFilterInput {
  eq: TrainingStatus @cost(weight: "10")
  neq: TrainingStatus @cost(weight: "10")
  in: [TrainingStatus!] @cost(weight: "10")
  nin: [TrainingStatus!] @cost(weight: "10")
}

input UpdateWorkerInput {
  id: Int!
  name: String
  company: String
  dateOfBirth: Date
  trainingIds: [Int!]
  tradeIds: [Int!]
  skillIds: [Int!]
  manHours: Int
  rating: Float
  gender: String
  phoneNumber: String
  mpesaNumber: String
  email: String
  inductionDate: DateTime
  medicalCheckDate: DateTime
  profilePicture: Upload
  signature: Upload
  documents: [DocumentFileInput!]
}

input UpdateWorkerWithTrainingInput {
  id: Int!
  name: String
  company: String
  nationalId: String
  dateOfBirth: Date
  trainings: [WorkerTrainingInput!]
  tradeIds: [Int!]
  skillIds: [Int!]
  manHours: Int
  rating: Float
  gender: String
  phoneNumber: String
  mpesaNumber: String
  email: String
  inductionDate: DateTime
  medicalCheckDate: DateTime
  profilePicture: Upload
  signature: Upload
  documents: [DocumentFileInput!]
}

input WorkerFilterInput {
  and: [WorkerFilterInput!]
  or: [WorkerFilterInput!]
  id: IntOperationFilterInput
  name: StringOperationFilterInput
  company: StringOperationFilterInput
  dateOfBirth: DateOperationFilterInput
  manHours: IntOperationFilterInput
  inductionDate: DateTimeOperationFilterInput
  profilePictureFileId: IntOperationFilterInput
  profilePictureFile: FileMetadataFilterInput
  signatureFileId: IntOperationFilterInput
  signatureFile: FileMetadataFilterInput
  documentFiles: ListFilterInputTypeOfDocumentFileFilterInput
  medicalCheckDate: DateTimeOperationFilterInput
  rating: FloatOperationFilterInput
  gender: StringOperationFilterInput
  nationalId: StringOperationFilterInput
  phoneNumber: StringOperationFilterInput
  email: StringOperationFilterInput
  mpesaNumber: StringOperationFilterInput
  trainings: ListFilterInputTypeOfTrainingFilterInput
  workerTrainings: ListFilterInputTypeOfWorkerTrainingFilterInput
  trades: ListFilterInputTypeOfTradeFilterInput
  skills: ListFilterInputTypeOfSkillFilterInput
  trainingHistory: ListFilterInputTypeOfWorkerTrainingHistoryFilterInput
  incidents: ListFilterInputTypeOfIncidentFilterInput
  age: IntOperationFilterInput
  trainingsCompleted: IntOperationFilterInput
  createdAt: DateTimeOperationFilterInput
  createdBy: StringOperationFilterInput
  updatedAt: DateTimeOperationFilterInput
  updatedBy: StringOperationFilterInput
  isDeleted: BooleanOperationFilterInput
  deletedAt: DateTimeOperationFilterInput
  deletedBy: StringOperationFilterInput
}

input WorkerTrainingFilterInput {
  and: [WorkerTrainingFilterInput!]
  or: [WorkerTrainingFilterInput!]
  workerId: IntOperationFilterInput
  worker: WorkerFilterInput
  trainingId: IntOperationFilterInput
  training: TrainingFilterInput
  documentFiles: ListFilterInputTypeOfDocumentFileFilterInput
  notes: StringOperationFilterInput
  assignedDate: DateTimeOperationFilterInput
  createdAt: DateTimeOperationFilterInput
  createdBy: StringOperationFilterInput
  updatedAt: DateTimeOperationFilterInput
  updatedBy: StringOperationFilterInput
  isDeleted: BooleanOperationFilterInput
  deletedAt: DateTimeOperationFilterInput
  deletedBy: StringOperationFilterInput
}

input WorkerTrainingHistoryFilterInput {
  and: [WorkerTrainingHistoryFilterInput!]
  or: [WorkerTrainingHistoryFilterInput!]
  id: IntOperationFilterInput
  workerId: IntOperationFilterInput
  worker: WorkerFilterInput
  trainingId: IntOperationFilterInput
  training: TrainingFilterInput
  completionDate: DateTimeOperationFilterInput
  expiryDate: DateTimeOperationFilterInput
  status: TrainingStatusOperationFilterInput
  notes: StringOperationFilterInput
  certificateUrl: StringOperationFilterInput
  score: DecimalOperationFilterInput
  createdAt: DateTimeOperationFilterInput
  createdBy: StringOperationFilterInput
  updatedAt: DateTimeOperationFilterInput
  updatedBy: StringOperationFilterInput
  isExpired: BooleanOperationFilterInput
  isExpiringSoon: BooleanOperationFilterInput
  daysUntilExpiry: IntOperationFilterInput
}

input WorkerTrainingInput {
  trainingId: Int!
  notes: String
  documents: [DocumentFileInput!]
}

enum AllowedFileType {
  JPEG
  JPG
  PNG
  GIF
  BMP
  WEBP
  PDF
  DOC
  DOCX
  XLS
  XLSX
  CSV
  TXT
}

enum BucketName {
  PROFILE_PICTURE
  CERTIFICATION
  SIGNATURES
  TEMP
  DOCS
}

enum IncidentStatus {
  REPORTED
  INVESTIGATING
  RESOLVED
  CLOSED
  PENDING
}

enum InspectionStatus {
  NOT_REQUIRED
  PENDING
  IN_PROGRESS
  COMPLETED
  FAILED
  REWORK_REQUIRED
}

enum JobStatus {
  REQUESTED
  BLOCKED
  PENDING_APPROVAL
  APPROVED
  DISAPPROVED
  FINISHED
}

enum PermitStatus {
  DRAFTED
  PENDING_APPROVAL
  OPENED
  CANCELLED
  PENDING_CLOSURE
  CLOSED
  VOIDED
  DISAPPROVED
  EXPIRED
}

enum PermitType {
  GENERAL_WORK_PERMIT
  HOT_WORK_PERMIT
  CONFINED_SPACE_ENTRY_PERMIT
  WORK_AT_HEIGHT_PERMIT
  EXCAVATION_PERMIT
}

enum TaskPriority {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum TaskStatus {
  TODO
  IN_PROGRESS
  COMPLETED
  CANCELLED
  ON_HOLD
}

enum ToolboxStatus {
  FILLED
  PENDING_CLOSURE
  CLOSED
}

enum TrainingStatus {
  SCHEDULED
  ACTIVE
  COMPLETED
  EXPIRED
  CANCELLED
}

"The purpose of the `cost` directive is to define a `weight` for GraphQL types, fields, and arguments. Static analysis can use these weights when calculating the overall cost of a query or response."
directive @cost(
  "The `weight` argument defines what value to add to the overall cost for every appearance, or possible appearance, of a type, field, argument, etc."
  weight: String!
) on SCALAR | OBJECT | FIELD_DEFINITION | ARGUMENT_DEFINITION | ENUM | INPUT_FIELD_DEFINITION

"The `@specifiedBy` directive is used within the type system definition language to provide a URL for specifying the behavior of custom scalar definitions."
directive @specifiedBy(
  "The specifiedBy URL points to a human-readable specification. This field will only read a result for scalar types."
  url: String!
) on SCALAR

"The `Date` scalar represents an ISO-8601 compliant date type."
scalar Date

"The `DateTime` scalar represents an ISO-8601 compliant date time type."
scalar DateTime @specifiedBy(url: "https://www.graphql-scalars.com/date-time")

"The `Decimal` scalar type represents a decimal floating-point number."
scalar Decimal

"The `Long` scalar type represents non-fractional signed whole 64-bit numeric values. Long can represent values between -(2^63) and 2^63 - 1."
scalar Long

"The `Upload` scalar type represents a file upload."
scalar Upload
