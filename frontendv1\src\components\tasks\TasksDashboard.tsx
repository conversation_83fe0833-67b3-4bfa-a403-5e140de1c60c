import React, { useState, useEffect } from "react";
import { useNavigate } from 'react-router-dom';
import {
  ClipboardList,
  Clock,
  CheckCircle,
  Plus,
  ClipboardCheck,
  XCircle,
  Archive,
  List
} from 'lucide-react';
import { TaskStats, Task } from '../../types/tasks';
import TaskStatusBadge from './shared/TaskStatusBadge';
import TaskPriorityBadge from './shared/TaskPriorityBadge';
// import PermitStatusBadge from '../permits/shared/PermitStatusBadge';


interface TasksDashboardProps {
	siteId: string;
	onNavigateToTab: (tabId: string) => void;
}

// Mock data - replace with actual API calls
const mockTaskStats: TaskStats = {
	totalTasks: 42,
	todoTasks: 8,           // Opened tasks (created but not requested)
	permitPendingTasks: 12, // Requested tasks (pending approval)
	permitApprovedTasks: 15, // Approved tasks (ready to start)
	inProgressTasks: 12,    // In-progress tasks
	blockedTasks: 2,        // Blocked tasks
	completedTasks: 28,     // Completed tasks
	cancelledTasks: 1,      // Cancelled tasks
	overdueTasks: 4,        // Overdue tasks
	tasksCompletedToday: 6,
	averageCompletionTime: 18.5,
	onTimeCompletionRate: 87.5,
	productivityScore: 92.3,
};

const mockRecentTasks: Task[] = [
	{
		id: "task-1",
		taskNumber: "TSK-2024-001",
		title: "Concrete Pouring - Foundation Level 1",
		description: "Pour concrete for foundation at Level 1, Zone A",
		category: "construction",
		location: "Zone A - Level 1",
		siteId: "site-1",
		plannedStartDate: new Date("2024-01-15T08:00:00"),
		plannedEndDate: new Date("2024-01-15T16:00:00"),
		actualStartDate: new Date("2024-01-15T08:15:00"),
		estimatedDuration: 8,
		actualDuration: 7.5,
		status: "in-progress",
		priority: "high",
		progressPercentage: 65,
		createdBy: "supervisor-1",
		createdByName: "John Smith",
		assignedSupervisor: "supervisor-1",
		assignedSupervisorName: "John Smith",
		assignedWorkers: [],
		dependencies: [],
		requiresPermit: false,
		riskLevel: "medium",
		safetyRequirements: [],
		requiredPPE: [],
		requiredTrainings: [],
		requiredCertifications: [],
		ramsDocuments: [],
		attachments: [],
		qualityChecks: [],
		complianceRequirements: [],
		createdAt: new Date("2024-01-14T16:00:00"),
		updatedAt: new Date("2024-01-15T08:15:00"),
		history: [],
		tags: [],
		customFields: {},
	},
	{
		id: "task-2",
		taskNumber: "TSK-2024-002",
		title: "Electrical Wiring - Floor 2",
		description: "Install electrical wiring for Floor 2 offices",
		category: "electrical",
		location: "Zone B - Floor 2",
		siteId: "site-1",
		plannedStartDate: new Date("2024-01-16T09:00:00"),
		plannedEndDate: new Date("2024-01-16T17:00:00"),
		estimatedDuration: 8,
		status: "permit-pending",
		priority: "medium",
		progressPercentage: 0,
		createdBy: "supervisor-2",
		createdByName: "Sarah Johnson",
		assignedSupervisor: "supervisor-2",
		assignedSupervisorName: "Sarah Johnson",
		assignedWorkers: [],
		dependencies: [],
		requiresPermit: true,
		permitStatus: "pending-approval",
		riskLevel: "high",
		safetyRequirements: [],
		requiredPPE: [],
		requiredTrainings: [],
		requiredCertifications: [],
		ramsDocuments: [],
		attachments: [],
		qualityChecks: [],
		complianceRequirements: [],
		createdAt: new Date("2024-01-15T14:00:00"),
		updatedAt: new Date("2024-01-15T14:00:00"),
		history: [],
		tags: [],
		customFields: {},
	},
];



const TasksDashboard: React.FC<TasksDashboardProps> = ({ siteId, onNavigateToTab }) => {
  const navigate = useNavigate();
  const [stats] = useState<TaskStats>(mockTaskStats);
  const [recentTasks] = useState<Task[]>(mockRecentTasks);


  useEffect(() => {
    // Fetch task and permit statistics
    // This would be replaced with actual API calls
    console.log(`Fetching task and permit data for site ${siteId}`);
  }, [siteId]);





  return (
    <div className="space-y-6">
      {/* Task Workflow Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm font-medium text-gray-500">Total Tasks</p>
              <p className="mt-2 text-2xl font-semibold text-gray-900">{stats.totalTasks}</p>
            </div>
            <div className="text-blue-600">
              <ClipboardList className="h-6 w-6" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm font-medium text-gray-500">Requested</p>
              <p className="mt-2 text-2xl font-semibold text-gray-900">{stats.permitPendingTasks}</p>
            </div>
            <div className="text-yellow-500">
              <Clock className="h-6 w-6" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm font-medium text-gray-500">Approved</p>
              <p className="mt-2 text-2xl font-semibold text-gray-900">{stats.permitApprovedTasks}</p>
            </div>
            <div className="text-green-600">
              <CheckCircle className="h-6 w-6" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm font-medium text-gray-500">In Progress</p>
              <p className="mt-2 text-2xl font-semibold text-gray-900">{stats.inProgressTasks}</p>
            </div>
            <div className="text-blue-500">
              <Clock className="h-6 w-6" />
            </div>
          </div>
        </div>
      </div>



      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <button
          onClick={() => navigate(`/sites/${siteId}/tasks/new`)}
          className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow text-left"
        >
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Plus className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-gray-900">New Task</h3>
              <p className="text-xs text-gray-500">Create a new task</p>
            </div>
          </div>
        </button>

        <button
          onClick={() => navigate(`/sites/${siteId}/tasks/review`)}
          className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow text-left"
        >
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ClipboardCheck className="h-6 w-6 text-orange-600" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-gray-900">Review Tasks</h3>
              <p className="text-xs text-gray-500">Review requested tasks</p>
            </div>
          </div>
        </button>

        <button
          onClick={() => navigate(`/sites/${siteId}/tasks/approve`)}
          className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow text-left"
        >
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CheckCircle className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-gray-900">Approve Tasks</h3>
              <p className="text-xs text-gray-500">Approve pending tasks</p>
            </div>
          </div>
        </button>

        <button
          onClick={() => navigate(`/sites/${siteId}/tasks/close`)}
          className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow text-left"
        >
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Archive className="h-6 w-6 text-purple-600" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-gray-900">Close Tasks</h3>
              <p className="text-xs text-gray-500">Close approved tasks</p>
            </div>
          </div>
        </button>
      </div>

      {/* Additional Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <button
          onClick={() => navigate(`/sites/${siteId}/tasks/all`)}
          className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow text-left"
        >
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <List className="h-8 w-8 text-gray-600" />
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-medium text-gray-900">All Tasks</h3>
              <p className="text-sm text-gray-500">View and filter all tasks</p>
            </div>
          </div>
        </button>

        <button
          onClick={() => onNavigateToTab('requests')}
          className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow text-left"
        >
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Clock className="h-8 w-8 text-yellow-600" />
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-medium text-gray-900">View Requests</h3>
              <p className="text-sm text-gray-500">Review pending task requests</p>
            </div>
          </div>
        </button>

        <button
          onClick={() => onNavigateToTab('active')}
          className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow text-left"
        >
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ClipboardList className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-medium text-gray-900">Active Tasks</h3>
              <p className="text-sm text-gray-500">View all active tasks</p>
            </div>
          </div>
        </button>
      </div>

			{/* Important Task Lists */}
			<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
				{/* Requested Tasks */}
				<div className="bg-white rounded-lg border border-gray-200 shadow-sm">
					<div className="px-6 py-4 border-b border-gray-200">
						<div className="flex justify-between items-center">
							<h3 className="text-lg font-medium text-gray-900">
								Requested Tasks
							</h3>
							<button
								onClick={() => onNavigateToTab("requests")}
								className="text-sm text-yellow-600 hover:text-yellow-800"
							>
								View All
							</button>
						</div>
					</div>
					<div className="divide-y divide-gray-200">
						{recentTasks.filter(task => task.status === 'permit-pending').slice(0, 5).map((task) => (
							<div
								key={task.id}
								className="px-6 py-4 hover:bg-gray-50 cursor-pointer transition-colors"
								onClick={() => navigate(`/sites/${siteId}/tasks/request/${task.id}`)}
							>
								<div className="flex justify-between items-start">
									<div className="flex-1">
										<div className="flex items-center space-x-3">
											<h4 className="text-sm font-medium text-gray-900 hover:text-blue-600">
												{task.title}
											</h4>
											<span className="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800 font-medium">
												PENDING APPROVAL
											</span>
											<TaskPriorityBadge priority={task.priority} size="sm" />
										</div>
										<p className="text-sm text-gray-500 mt-1">
											{task.category} • {task.location}
										</p>
										<p className="text-xs text-gray-400 mt-1">
											Requested by {task.assignedSupervisorName} • {task.taskNumber}
										</p>
									</div>
									<div className="text-right">
										<p className="text-xs text-gray-500">
											{task.createdAt.toLocaleDateString()}
										</p>
									</div>
								</div>
							</div>
						))}
						{recentTasks.filter(task => task.status === 'permit-pending').length === 0 && (
							<div className="px-6 py-8 text-center text-gray-500">
								<Clock className="h-8 w-8 mx-auto mb-2 text-gray-300" />
								<p className="text-sm">No pending requests</p>
							</div>
						)}
					</div>
				</div>

        {/* Approved Tasks */}
        <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium text-gray-900">Approved Tasks</h3>
              <button
                onClick={() => onNavigateToTab("active")}
                className="text-sm text-green-600 hover:text-green-800"
              >
                View All
              </button>
            </div>
          </div>
          <div className="divide-y divide-gray-200">
            {recentTasks.filter(task => task.status === 'permit-approved' || task.status === 'in-progress').slice(0, 5).map((task) => (
              <div
                key={task.id}
                className="px-6 py-4 hover:bg-gray-50 cursor-pointer transition-colors"
                onClick={() => navigate(`/sites/${siteId}/tasks/${task.id}`)}
              >
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      <h4 className="text-sm font-medium text-gray-900 hover:text-blue-600">
                        {task.title}
                      </h4>
                      <TaskStatusBadge status={task.status} size="sm" />
                      <TaskPriorityBadge priority={task.priority} size="sm" />
                    </div>
                    <p className="text-sm text-gray-500 mt-1">
                      {task.category} • {task.location}
                    </p>
                    <p className="text-xs text-gray-400 mt-1">
                      Requested by {task.createdByName} • {task.taskNumber}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-xs text-gray-500">
                      {task.status === 'in-progress' ? 'In Progress' : 'Ready to start'}
                    </p>
                  </div>
                </div>
              </div>
            ))}
            {recentTasks.filter(task => task.status === 'permit-approved' || task.status === 'in-progress').length === 0 && (
              <div className="px-6 py-8 text-center text-gray-500">
                <CheckCircle className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                <p className="text-sm">No approved tasks</p>
              </div>
            )}
          </div>
        </div>
      </div>


    </div>
  );
};

export default TasksDashboard;
