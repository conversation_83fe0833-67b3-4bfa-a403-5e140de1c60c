import React, { useState, useEffect, useRef } from 'react';
import { TrainingDocUploader } from '../components/workers/CreateWorkerForm';
import { useNavigate, useParams } from 'react-router-dom';
import { useMutation, useQuery } from '@apollo/client';
import { toast } from 'react-toastify';
import {
  ClipboardCheck,
  AlertTriangle,
  Shield,
  FileText,
  Upload,
  Save,
  X,
  Plus,
  Minus,
  Ban,
  Eye,
  Calendar,
  User,
  Users,
  Clock
} from 'lucide-react';
import FloatingCard from '../components/layout/FloatingCard';
import { REVIEW_JOB, BLOCK_JOB, REVIEW_JOBS, BLOCK_JOBS } from '../graphql/mutations';
import { GET_REQUESTED_JOBS } from '../graphql/queries';
import { DocumentFileInput, PermitType } from '../types/graphql';

interface Job {
  id: number;
  title: string;
  description: string;
  status: string;
  timeForCompletion: string;
  startDate: string;
  requestedBy: {
    id: number;
    name: string;
  };
  chiefEngineer: {
    id: number;
    name: string;
  };
  workers: Array<{
    id: number;
    name: string;
    company: string;
  }>;
  requestedDate: string;
  createdAt: string;
}

interface Hazard {
  description: string;
  controlMeasures: string[];
}

// interface DocumentFile {
//   name: string;
//   file: File;
// }

interface TaskReview {
  jobId: number;
  hazards: Hazard[];
  documents: DocumentFileInput[];
  requiredPermits: PermitType[];
  isBlocked: boolean;
}

const PERMIT_TYPES: PermitType[] = [
  "GENERAL_WORK_PERMIT",
  "HOT_WORK_PERMIT",
  "CONFINED_SPACE_ENTRY_PERMIT",
  "WORK_AT_HEIGHT_PERMIT",
  "EXCAVATION_PERMIT",
];

const PERMIT_LABELS = {
  "GENERAL_WORK_PERMIT": "General Work Permit",
  "HOT_WORK_PERMIT": "Hot Work Permit",
  "CONFINED_SPACE_ENTRY_PERMIT":'Confined Space Entry Permit',
  "WORK_AT_HEIGHT_PERMIT":'Work at Height Permit',
  "EXCAVATION_PERMIT":'Excavation Permit',

  // GENERAL_WORK: 'General Work Permit',
  // HOT_WORK: 'Hot Work Permit',
  // CONFINED_SPACE: 'Confined Space Permit',
  // WORK_AT_HEIGHT: 'Work at Height Permit',
  // EXCAVATION: 'Excavation Permit'
};

const ReviewTaskPage: React.FC = () => {
  const navigate = useNavigate();
  const { siteId } = useParams<{ siteId: string }>();

  const [taskReviews, setTaskReviews] = useState<Record<number, TaskReview>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [expandedTasks, setExpandedTasks] = useState<Set<number>>(new Set());

  const { data: jobsData, loading: jobsLoading, refetch } = useQuery(GET_REQUESTED_JOBS);

  const [reviewJobs] = useMutation(REVIEW_JOBS, {
    onCompleted: () => {
      toast.success('Tasks reviewed successfully!');
      refetch();
    },
    onError: (error) => {
      toast.error(`Failed to review tasks: ${error.message}`);
    }
  });

  const [blockJobs] = useMutation(BLOCK_JOBS, {
    onCompleted: () => {
      toast.success('Tasks blocked successfully!');
      refetch();
    },
    onError: (error) => {
      toast.error(`Failed to block tasks: ${error.message}`);
    }
  });

  const jobs: Job[] = jobsData?.requestedJobs || [];

  const initializeTaskReview = (jobId: number) => {
    if (!taskReviews[jobId]) {
      setTaskReviews(prev => ({
        ...prev,
        [jobId]: {
          jobId,
          hazards: [],
          documents: [],
          requiredPermits: ['GENERAL_WORK_PERMIT'], // Default to general permit
          isBlocked: false
        }
      }));
    }
  };

  const toggleTaskExpansion = (jobId: number) => {
    setExpandedTasks(prev => {
      const newSet = new Set(prev);
      if (newSet.has(jobId)) {
        newSet.delete(jobId);
      } else {
        newSet.add(jobId);
        initializeTaskReview(jobId);
      }
      return newSet;
    });
  };

  const updateTaskReview = (jobId: number, updates: Partial<TaskReview>) => {
    setTaskReviews(prev => ({
      ...prev,
      [jobId]: { ...prev[jobId], ...updates }
    }));
  };

  const addHazard = (jobId: number) => {
    const review = taskReviews[jobId];
    if (review) {
      updateTaskReview(jobId, {
        hazards: [...review.hazards, { description: '', controlMeasures: [''] }]
      });
    }
  };

  const removeHazard = (jobId: number, hazardIndex: number) => {
    const review = taskReviews[jobId];
    if (review) {
      updateTaskReview(jobId, {
        hazards: review.hazards.filter((_, index) => index !== hazardIndex)
      });
    }
  };

  const updateHazard = (jobId: number, hazardIndex: number, description: string) => {
    const review = taskReviews[jobId];
    if (review) {
      const updatedHazards = [...review.hazards];
      updatedHazards[hazardIndex] = { ...updatedHazards[hazardIndex], description };
      updateTaskReview(jobId, { hazards: updatedHazards });
    }
  };

  const addControlMeasure = (jobId: number, hazardIndex: number) => {
    const review = taskReviews[jobId];
    if (review) {
      const updatedHazards = [...review.hazards];
      updatedHazards[hazardIndex] = {
        ...updatedHazards[hazardIndex],
        controlMeasures: [...updatedHazards[hazardIndex].controlMeasures, '']
      };
      updateTaskReview(jobId, { hazards: updatedHazards });
    }
  };

  const removeControlMeasure = (jobId: number, hazardIndex: number, measureIndex: number) => {
    const review = taskReviews[jobId];
    if (review) {
      const updatedHazards = [...review.hazards];
      updatedHazards[hazardIndex] = {
        ...updatedHazards[hazardIndex],
        controlMeasures: updatedHazards[hazardIndex].controlMeasures.filter((_, index) => index !== measureIndex)
      };
      updateTaskReview(jobId, { hazards: updatedHazards });
    }
  };

  const updateControlMeasure = (jobId: number, hazardIndex: number, measureIndex: number, value: string) => {
    const review = taskReviews[jobId];
    if (review) {
      const updatedHazards = [...review.hazards];
      const updatedMeasures = [...updatedHazards[hazardIndex].controlMeasures];
      updatedMeasures[measureIndex] = value;
      updatedHazards[hazardIndex] = {
        ...updatedHazards[hazardIndex],
        controlMeasures: updatedMeasures
      };
      updateTaskReview(jobId, { hazards: updatedHazards });
    }
  };

  const handleDocumentUpload = (jobId: number, file: File, name: string) => {
    const review = taskReviews[jobId];
    if (review) {
      updateTaskReview(jobId, {
        documents: [...review.documents, { name, file, isPublic: true }]
      });
    }
  };

  const removeDocument = (jobId: number, docIndex: number) => {
    const review = taskReviews[jobId];
    if (review) {
      updateTaskReview(jobId, {
        documents: review.documents.filter((_, index) => index !== docIndex)
      });
    }
  };

  const togglePermit = (jobId: number, permit: PermitType) => {
    const review = taskReviews[jobId];
    if (review) {
      const updatedPermits = review.requiredPermits.includes(permit)
        ? review.requiredPermits.filter(p => p !== permit)
        : [...review.requiredPermits, permit];
      updateTaskReview(jobId, { requiredPermits: updatedPermits });
    }
  };

  const toggleBlockStatus = (jobId: number) => {
    const review = taskReviews[jobId];
    if (review) {
      updateTaskReview(jobId, { isBlocked: !review.isBlocked });
    }
  };

  const handleSaveChanges = async () => {
    const reviewInputs = [];
    const blockInputs = [];

    for (const [jobIdStr, review] of Object.entries(taskReviews)) {
      const jobId = parseInt(jobIdStr);

      if (review.isBlocked) {
        blockInputs.push({
          jobId,
          blockedById: 1 // Using 1 as per requirement
        });
      } else {
        // Only add to review if there are hazards, documents, or permits
        if (review.hazards.length > 0 || review.documents.length > 0 || review.requiredPermits.length > 0) {
          reviewInputs.push({
            jobId,
            reviewedById: 1, // Using 1 as per requirement
            hazards: review.hazards.map(h => ({
              description: h.description,
              controlMeasures: h.controlMeasures.filter(cm => cm.trim() !== '')
            })).filter(h => h.description.trim() !== ''),
            documents: review.documents,
            requiredPermits: review.requiredPermits
          });
        }
      }
    }

    setIsSubmitting(true);
    try {
      if (blockInputs.length > 0) {
        await blockJobs({ variables: { inputs: blockInputs } });
      }
      if (reviewInputs.length > 0) {
        // console.log('Review inputs:', reviewInputs);
        await reviewJobs({ variables: { inputs: reviewInputs } });
      }

      // Clear the reviews after successful submission
      setTaskReviews({});
      setExpandedTasks(new Set());
    } catch (error) {
      console.error('Error saving changes:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const breadcrumbs = [
    { name: 'Dashboard', path: '/' },
    { name: 'Tasks', path: `/sites/${siteId}/tasks` },
    { name: 'Review Tasks', path: `/sites/${siteId}/tasks/review` }
  ];

  if (jobsLoading) {
    return (
      <FloatingCard title="Review Tasks" breadcrumbs={breadcrumbs}>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </FloatingCard>
    );
  }

  return (
    <FloatingCard title="Review Tasks" breadcrumbs={breadcrumbs}>
      <div className="space-y-6">
        {jobs.length === 0 ? (
          <div className="text-center py-12">
            <ClipboardCheck className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Tasks to Review</h3>
            <p className="text-gray-500">There are currently no tasks waiting for review.</p>
          </div>
        ) : (
          <>
            <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <ClipboardCheck className="h-5 w-5 text-blue-400" />
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-blue-800">
                    Review Instructions
                  </h3>
                  <div className="mt-2 text-sm text-blue-700">
                    <p>
                      Review each task below. You can block tasks, add hazards with control measures,
                      attach documents, and select required permits. Changes are saved when you click
                      "Save Changes" at the bottom.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {jobs.map((job) => {
              const isExpanded = expandedTasks.has(job.id);
              const review = taskReviews[job.id];

              return (
                <div key={job.id} className="border border-gray-200 rounded-lg overflow-hidden">
                  <div className="bg-gray-50 px-6 py-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-4">
                          <h3 className="text-lg font-medium text-gray-900">{job.title}</h3>
                          {review?.isBlocked && (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                              <Ban className="h-3 w-3 mr-1" />
                              Blocked
                            </span>
                          )}
                        </div>
                        <div className="mt-2 flex items-center space-x-6 text-sm text-gray-500">
                          {/* <div className="flex items-center">
                            <User className="h-4 w-4 mr-1" />
                            Requested by: {job.requestedBy.name}
                          </div> */}
                          <div className="flex items-center">
                            <User className="h-4 w-4 mr-1" />
                            Chief Engineer: {job.chiefEngineer.name}
                          </div>
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-1" />
                            Start: {new Date(job.startDate).toLocaleDateString()}
                          </div>
                          <div className="flex items-center">
                            <Clock className="h-4 w-4 mr-1" />
                            Duration: {job.timeForCompletion} days
                          </div>
                        </div>
                      </div>
                      <button
                        onClick={() => toggleTaskExpansion(job.id)}
                        className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        {isExpanded ? 'Collapse' : 'Review'}
                      </button>
                    </div>
                  </div>

                  {isExpanded && (
                    <div className="px-6 py-4 space-y-6">
                      {/* Task Description */}
                      <div>
                        <h4 className="text-sm font-medium text-gray-900 mb-2">Description</h4>
                        <p className="text-sm text-gray-700 bg-gray-50 p-3 rounded-md">{job.description}</p>
                      </div>

                      {/* Workers */}
                      <div>
                        <h4 className="text-sm font-medium text-gray-900 mb-2">
                          <Users className="h-4 w-4 inline mr-1" />
                          Assigned Workers ({job.workers.length})
                        </h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                          {job.workers.map((worker) => (
                            <div key={worker.id} className="bg-gray-50 p-2 rounded text-sm">
                              <p className="font-medium">{worker.name}</p>
                              <p className="text-gray-600">{worker.company}</p>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Block/Unblock Toggle */}
                      <div className="flex items-center justify-between p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                        <div>
                          <h4 className="text-sm font-medium text-yellow-800">Block Task</h4>
                          <p className="text-sm text-yellow-700">
                            Block this task to prevent further processing. Blocked tasks will be grayed out.
                          </p>
                        </div>
                        <button
                          onClick={() => toggleBlockStatus(job.id)}
                          className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${review?.isBlocked
                              ? 'bg-red-600 text-white hover:bg-red-700'
                              : 'bg-yellow-600 text-white hover:bg-yellow-700'
                            }`}
                        >
                          <Ban className="h-4 w-4 inline mr-2" />
                          {review?.isBlocked ? 'Unblock Task' : 'Block Task'}
                        </button>
                      </div>

                      {!review?.isBlocked && (
                        <>
                          {/* Hazards Section */}
                          <div>
                            <div className="flex items-center justify-between mb-4">
                              <h4 className="text-sm font-medium text-gray-900">
                                <AlertTriangle className="h-4 w-4 inline mr-1" />
                                Hazards & Control Measures
                              </h4>
                              <button
                                onClick={() => addHazard(job.id)}
                                className="px-3 py-1 bg-orange-600 text-white text-sm rounded-md hover:bg-orange-700"
                              >
                                <Plus className="h-4 w-4 inline mr-1" />
                                Add Hazard
                              </button>
                            </div>

                            {review?.hazards.map((hazard, hazardIndex) => (
                              <div key={hazardIndex} className="border border-gray-200 rounded-md p-4 mb-4">
                                <div className="flex items-start justify-between mb-3">
                                  <div className="flex-1 mr-4">
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                      Hazard Description
                                    </label>
                                    <textarea
                                      value={hazard.description}
                                      onChange={(e) => updateHazard(job.id, hazardIndex, e.target.value)}
                                      rows={2}
                                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                      placeholder="Describe the hazard..."
                                    />
                                  </div>
                                  <button
                                    onClick={() => removeHazard(job.id, hazardIndex)}
                                    className="text-red-500 hover:text-red-700 mt-6"
                                  >
                                    <X className="h-4 w-4" />
                                  </button>
                                </div>

                                <div>
                                  <div className="flex items-center justify-between mb-2">
                                    <label className="block text-sm font-medium text-gray-700">
                                      <Shield className="h-4 w-4 inline mr-1" />
                                      Control Measures
                                    </label>
                                    <button
                                      onClick={() => addControlMeasure(job.id, hazardIndex)}
                                      className="px-2 py-1 bg-green-600 text-white text-xs rounded hover:bg-green-700"
                                    >
                                      <Plus className="h-3 w-3 inline mr-1" />
                                      Add Measure
                                    </button>
                                  </div>

                                  {hazard.controlMeasures.map((measure, measureIndex) => (
                                    <div key={measureIndex} className="flex items-center space-x-2 mb-2">
                                      <input
                                        type="text"
                                        value={measure}
                                        onChange={(e) => updateControlMeasure(job.id, hazardIndex, measureIndex, e.target.value)}
                                        className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        placeholder="Control measure..."
                                      />
                                      <button
                                        onClick={() => removeControlMeasure(job.id, hazardIndex, measureIndex)}
                                        className="text-red-500 hover:text-red-700"
                                      >
                                        <Minus className="h-4 w-4" />
                                      </button>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            ))}

                            {(!review?.hazards || review.hazards.length === 0) && (
                              <div className="text-center py-8 text-gray-500 border-2 border-dashed border-gray-300 rounded-md">
                                <AlertTriangle className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                                <p>No hazards added yet. Click "Add Hazard" to get started.</p>
                              </div>
                            )}
                          </div>

                          {/* Documents Section */}
                          <div>
                            <div className="flex justify-between mb-4 flex-col gap-2">
                              <h4 className="text-sm font-medium text-gray-900">
                                <FileText className="h-4 w-4 inline mr-1" />
                                Documents
                              </h4>
                              <label className="px-3 py-1 text-sm rounded-md hover:bg-blue-700 cursor-pointer">

                                <TrainingDocUploader
                                  trainingId={job.id}
                                  onAdd={handleDocumentUpload}
                                />

                              </label>
                            </div>

                            {review?.documents && review.documents.length > 0 ? (
                              <div className="space-y-2">
                                {review.documents.map((doc, docIndex) => (
                                  <div key={docIndex} className="flex items-center justify-between p-3 bg-gray-50 border border-gray-200 rounded-md">
                                    <div className="flex items-center space-x-3">
                                      <FileText className="h-4 w-4 text-gray-500" />
                                      <div>
                                        <p className="text-sm font-medium">{doc.name}</p>
                                        <p className="text-xs text-gray-500">{doc.file.name}</p>
                                      </div>
                                    </div>
                                    <button
                                      onClick={() => removeDocument(job.id, docIndex)}
                                      className="text-red-500 hover:text-red-700"
                                    >
                                      <X className="h-4 w-4" />
                                    </button>
                                  </div>
                                ))}
                              </div>
                            ) : (
                              <div className="text-center py-8 text-gray-500 border-2 border-dashed border-gray-300 rounded-md">
                                <FileText className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                                <p>No documents uploaded yet. Click "Choose File" to add files.</p>
                              </div>
                            )}
                          </div>

                          {/* Required Permits Section */}
                          <div>
                            <h4 className="text-sm font-medium text-gray-900 mb-4">
                              <ClipboardCheck className="h-4 w-4 inline mr-1" />
                              Required Permits
                            </h4>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                              {PERMIT_TYPES.map((permit) => (
                                <label key={permit} className="flex items-center space-x-3 p-3 border border-gray-200 rounded-md hover:bg-gray-50 cursor-pointer">
                                  <input
                                    type="checkbox"
                                    checked={review?.requiredPermits.includes(permit) || false}
                                    onChange={() => togglePermit(job.id, permit)}
                                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                  />
                                  <span className="text-sm font-medium text-gray-700">
                                    {PERMIT_LABELS[permit as keyof typeof PERMIT_LABELS]}
                                  </span>
                                </label>
                              ))}
                            </div>
                          </div>
                        </>
                      )}
                    </div>
                  )}
                </div>
              );
            })}

            {/* Save Changes Button */}
            <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
              <button
                onClick={() => navigate(`/sites/${siteId}/tasks`)}
                className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
              >
                <X className="h-4 w-4 inline mr-2" />
                Cancel
              </button>
              <button
                onClick={handleSaveChanges}
                disabled={isSubmitting || Object.keys(taskReviews).length === 0}
                className="px-4 py-2 bg-blue-600 text-white hover:bg-blue-700 disabled:bg-blue-400 rounded-md transition-colors"
              >
                <Save className="h-4 w-4 inline mr-2" />
                {isSubmitting ? 'Saving...' : 'Save Changes'}
              </button>
            </div>
          </>
        )}
      </div>
    </FloatingCard>
  );
};

export default ReviewTaskPage;
